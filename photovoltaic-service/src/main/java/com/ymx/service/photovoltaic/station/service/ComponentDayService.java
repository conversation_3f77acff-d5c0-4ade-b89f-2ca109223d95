package com.ymx.service.photovoltaic.station.service;


import com.ymx.service.photovoltaic.station.model.ComponentDayModel;
import com.ymx.service.photovoltaic.station.model.ComponentHourModel;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ComponentDayService {

	/**
	 * 根据批次号查询功率
	 * @param map
	 * @return
	 */
	List<ComponentHourModel> queryGroupPowerByBatchNo(Map<String, Object> map);

	List<ComponentDayModel> queryComponentByYear(Map<String, Object> map);
	List<ComponentDayModel> queryComponentByMonth(Map<String, Object> map);
	List<ComponentDayModel> queryComponentByDay(Map<String, Object> map);
	List<ComponentHourModel> queryComponentByXs(Map<String, Object> map);
	List<ComponentHourModel> queryDataByGroupId(Map<String, Object> map);
	List<ComponentHourModel> queryDataGroupByBatchNo(Map<String, Object> map);
	List<ComponentHourModel> queryTodayGroupCollectInfo(Map<String, Object> map);
	List<ComponentHourModel> queryTodayBatchNoCollectInfo(Map<String, Object> map);
	// 查询批次采集信息
	List<ComponentHourModel> queryBatchNoCollectInfo(Map<String, Object> map);
	// 查询电站今天的发电量
	BigDecimal queryTodayKwh(String powerStationId,String sunUpTime,String groupId);
	// 查询电站历史的发电量
	BigDecimal queryOldDayKwh(String powerStationId, String batchNo,String groupId);
	// 计算社会贡献
	Map<String, Object> calculateContribution(String memberId);

	PowerStationModel queryPowerStationInfo(String powerStationId);
	// 查询历史发电量
	BigDecimal queryHistoryKwh(String powerStationId, String day);

}
