package com.ymx.app.controller.station;

import com.ymx.common.common.result.CallResult;
import com.ymx.service.photovoltaic.station.model.*;
import com.ymx.service.photovoltaic.station.service.*;
import com.ymx.service.photovoltaic.warning.entity.WarningModel;
import com.ymx.service.photovoltaic.warning.service.WarningService;
import com.ymx.common.utils.ConfigUtil;
import com.ymx.common.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("component/v1")
public class ComponentV1AppController {

    @Resource
    private ComponentService componentService;
    @Resource
    private ComponentCollectService componentCollectService;
    @Resource
    private WarningService warningService;
    @Resource
    private ComponentGroupService componentGroupService;
    @Resource
    private ComponentDayService componentDayService;

    @Resource
    private CloudTerminalService cloudTerminalService;

    private static final Logger logger = LoggerFactory.getLogger(ComponentV1AppController.class);

    // 查询组串位置信息 告警信息 电站发电量  最多查询5次
    @RequestMapping(value = "/queryComponentGroupList.api")
    public CallResult queryComponentGroupList(@RequestParam(value = "powerStationId", required = true) String powerStationId,
                                              @RequestParam(value = "date", required = false) String date,
                                              @RequestParam(value = "sunUpTime", required = false) String sunUpTime,
                                              @RequestParam(value = "sunDownTime", required = false) String sunDownTime,
                                              @RequestParam(value = "from", required = false) String from) {

        CallResult callResult = CallResult.newInstance();
        // 查询组串的位置信息
        List<ShowGroupModel> showGroupModelList = componentGroupService.queryGroupPositionForApp(powerStationId);

        // 通过equipmentId 关联其他list
        List<WarningModel> warningModelList = warningService.queryWarnListByHashMap(powerStationId, null);
//
        if (warningModelList != null && warningModelList.size() > 0) {
            // 获取告警的设备id
            List<String> equipmentIdList = warningModelList.stream().map(WarningModel::getEquipmentId).collect(Collectors.toList());
            // 查询equipmentId对应的groupId
            List<String> groupIdList = componentService.queryGroupIdById(powerStationId, equipmentIdList);
            // 设置某个组串的告警状态
            Optional.ofNullable(showGroupModelList).
                    orElse(new ArrayList<>()).forEach(sgm -> {
                        if (groupIdList.contains(sgm.getGroupId())) {
                            sgm.setWarningStatus(2);
                        } else {
                            sgm.setWarningStatus(1);
                        }

                    });
        }

        BigDecimal kwh = new BigDecimal(0);
        if (from.equals("view")) {
            kwh = getPowerGeneration(null, powerStationId, date, sunUpTime);
        }

        Map<String, Object> map = new HashMap<>();
        // 组串位置信息
        map.put("data", showGroupModelList);
        // 电站id
        map.put("kwh", kwh);

        callResult.setReModel(map);
        return callResult;
    }


    /**
     *
     */
    // 手机app获取系统视图的接口  返回的信息包括组件的电气信息 组串的电气信息 总发电量  时间轴四个数据
    @RequestMapping(value = "/queryComponentList.api")
    public CallResult queryComponentList(@RequestParam(value = "powerStationId", required = true) String powerStationId,
                                         @RequestParam(value = "date", required = false) String date,
                                         @RequestParam(value = "type", required = false) String type,
                                         @RequestParam(value = "groupId", required = false) String groupId,
                                         @RequestParam(value = "sunUpTime", required = false) String sunUpTime,
                                         @RequestParam(value = "sunDownTime", required = false) String sunDownTime,
                                         @RequestParam(value = "from", required = false) String from) {

        if (from != null && from.equals("view")) {
            return querySystemViewData(groupId, powerStationId, date, sunUpTime, sunDownTime, type);
        } else {
            return queryControlViewData(groupId, powerStationId, date);
        }
    }


    // 返回组件控制视图需要的数据
    public CallResult querySystemViewData(String groupId, String powerStationId,
                                          String date, String sunUpTime, String sunDownTime, String type) {
        CallResult callResult = CallResult.newInstance();
        // 有了单个组件的当天的累计发电量后，电站发电量的计算方式可以随之优化
        BigDecimal dayKwh = getPowerGeneration(groupId, powerStationId, date, sunUpTime);
        Map<String, Object> map = getElectricData(groupId, powerStationId, date, sunUpTime, sunDownTime, type);

        map.put("dayKwh", dayKwh);
        // 查询分组信息
        if (groupId == null) {
            List<GroupModel> groupModelList = componentGroupService.queryGroupInfoForApp(powerStationId);
            map.put("groupList", groupModelList);
        }
        List<CloudTerminalModel> collectorList=cloudTerminalService.queryCollectorListByPowerId(powerStationId);
        map.put("collectorList",collectorList);
        callResult.setReModel(map);
        return callResult;
    }


    // 返回组件控制视图需要的数据
    public CallResult queryControlViewData(String groupId, String powerStationId,
                                           String date) {
        CallResult callResult = CallResult.newInstance();
        List<ComponentControlModel> componentControlList = getComPositionAndWarnStatus(groupId, powerStationId, date);
        Map<String, Object> map = new HashMap<>();
        map.put("data", componentControlList);
        callResult.setReModel(map);
        return callResult;
    }

    public BigDecimal getPowerGeneration(String groupId, String powerStationId,
                                         String date, String sunUpTime) {
        BigDecimal dayKwh;
        if (date != null && date.contains(":")) {
            dayKwh = componentDayService.queryTodayKwh(powerStationId, sunUpTime, groupId);
        } else {
            String batchNo = date.replace("-", "");
            dayKwh = componentDayService.queryOldDayKwh(powerStationId, batchNo, groupId);
        }
        return dayKwh;
    }

    //获取最大的批次号
    private String getMaxCollectTime(Map<String, Long> collectTimeMap) {
        List<String> keyList = collectTimeMap.keySet().stream().
                sorted(Comparator.comparing(collectTimeMap::get)).collect(Collectors.toList());
        if (keyList.size() == 1) {
            return keyList.get(0);
        } else {
            String firstKey = keyList.get(0);
            String secondKey = keyList.get(1);

            long firstValue = collectTimeMap.get(firstKey);
            long secondValue = collectTimeMap.get(secondKey);

            // 哪个批次的数据全就取哪个批次的
            if (firstValue > secondValue) {
                return firstKey;
            }
            if (firstValue < secondValue) {
                return secondKey;
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 如果两个批次的数据量相同 取最近的批次号
            LocalDateTime firstCollectTime = LocalDateTime.parse(firstKey, formatter);
            LocalDateTime secondCollectTime = LocalDateTime.parse(secondKey, formatter);
            if (firstCollectTime.isAfter(secondCollectTime)) {
                return firstKey;
            } else {
                return secondKey;
            }
        }

    }

    private Map<String, Object> getQueryMap(String powerStationId,String date) {
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", "_" + powerStationId);
        String timeAxisCreateTime;
        int timeStep = ConfigUtil.getTimeStep();

        if (date != null && date.contains(":")) {
            // 取出最近入库的两倍组件数量  防止入库时间不一致
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            LocalDateTime createTime = LocalDateTime.parse(date, dtf);
            createTime = createTime.plusMinutes(-timeStep);
            timeAxisCreateTime = createTime.withNano(0).toString().replace("T", " ");
            map.put("collectTimeStart", timeAxisCreateTime);
        } else {
            // 如果是过去的日期
            LocalTime now = LocalTime.now();
            LocalTime afterNow = now.plusMinutes(timeStep);
            timeAxisCreateTime = date + " " + now.withSecond(0).withNano(0);
            map.put("collectTimeStart", timeAxisCreateTime);
            map.put("collectTimeEnd", date + " " + afterNow.withSecond(0).withNano(0));
        }
        return map;
    }

    private List<ComponentViewModel> getComponentViewList(String groupId,String date,
                                                          List<ComponentControlModel> componentControlList,Map<String, Object> map) {
        if (groupId != null) {
            List<String> chipIdList = componentControlList.stream().map(ComponentControlModel::getChipId).collect(Collectors.toList());
            map.put("chipIdList", chipIdList);
        }

        List<ComponentViewModel> componentViewModelList;
        if (date != null && date.contains(":")) {
            map.put("limit", componentControlList.size() * 2);
            componentViewModelList = componentCollectService.queryLastCollectInfo(map);
            if (!componentViewModelList.isEmpty()) {
                // 获取每个批次的组件数量  最后是否需要batchNo 还是只需要采集时间
                Map<String, Long> collectTimeMap = Optional.of(componentViewModelList).
                        orElse(new ArrayList<>()).stream().
                        collect(Collectors.groupingBy(ComponentViewModel::getCollectTime, Collectors.counting()));

                collectTimeMap.forEach((k, v) -> {
                    logger.info("key:value = " + k + ":" + v);
                });
                String maxCollectTime = getMaxCollectTime(collectTimeMap);

                logger.info("maxCollectTime: " + maxCollectTime);
                // 用这个批次号去过滤组件
                componentViewModelList = Optional.of(componentViewModelList).
                        orElse(new ArrayList<>()).stream().
                        filter(componentViewModel -> componentViewModel.getCollectTime().equals(maxCollectTime))
                        .collect(Collectors.toList());
            }
        } else {
            componentViewModelList = componentCollectService.queryPastCollectInfo(map);
        }

        // key chipId value 为ComponentViewModel
        Map<String, ComponentViewModel> comViewModelMap = Optional.of(componentViewModelList).
                orElse(new ArrayList<>()).stream().
                collect(Collectors.toMap(ComponentViewModel::getChipId, v -> v, (a, b) -> a));

        List<ComponentViewModel> returnComViewModelList = new ArrayList<>();

        ComponentViewModel componentViewModel;
        // 循环componentControlList  合并电气信息对象
        for (ComponentControlModel componentControlModel : componentControlList) {
            componentViewModel = new ComponentViewModel(componentControlModel,
                    comViewModelMap.get(componentControlModel.getChipId()));
            returnComViewModelList.add(componentViewModel);
        }

        return returnComViewModelList;

    }

    // 返回系统视图需要的数据
    public Map<String, Object> getElectricData(String groupId, String powerStationId,
                                               String date, String sunUpTime, String sunDownTime, String type) {

        // 先获取位置和告警信息对象 再获取电气信息对象 然后合并两个对象
        List<ComponentControlModel> componentControlList = getComPositionAndWarnStatus(groupId, powerStationId, date);

        Map<String, Object> queryMap = getQueryMap(powerStationId,date);
        List<ComponentViewModel> componentViewModelList = getComponentViewList(groupId,date,componentControlList,queryMap);

        Map<String, Object> returnMap =new HashMap<>();
        returnMap.put("data", componentViewModelList);

        // 设置时间轴信息
        if (type.equals("1")) {
            String collectTime = null;
            if (!componentViewModelList.isEmpty()) {
                collectTime = componentViewModelList.get(0).getCollectTime();
                logger.info("first collect time:" + collectTime);
            }
            String timeAxisCreateTime = (String) queryMap.get("collectTimeStart");
            timeAxisCreateTime = collectTime != null ? collectTime : timeAxisCreateTime + ":00";

            returnMap.put("timeAxis", Collections.singletonList(timeAxisCreateTime));
            returnMap.put("timeAxisStep", ConfigUtil.getTimeStep());
            String day = timeAxisCreateTime.substring(0, 10);
            returnMap.put("timeAxisBeginTime", day + " " + sunUpTime + ":00");
            returnMap.put("timeAxisEndTime", day + " " + sunDownTime + ":00");
        }
        return returnMap;
    }

    // 查询组件位置和告警状态  考虑分组 日期不用考虑 告警都是取当天的
    private List<ComponentControlModel> getComPositionAndWarnStatus(String groupId, String powerStationId,
                                                                    String date) {
        Map<String, Object> map = new HashMap<>();
        map.put("powerStationId", powerStationId);

        List<ComponentControlModel> componentControlList;
        // 如果组串id为空 则查询整个电站 否则查询单个组串的
        if (groupId == null) {
            componentControlList = componentService.queryComponentLocation(powerStationId);
        } else {
            componentControlList = componentService.queryGroupComPosition(groupId);
            // 取出equipmentIdList 查询告警状态
            List<String> chipIdList = componentControlList.stream().
                    map(ComponentControlModel::getChipId).collect(Collectors.toList());
            if (chipIdList.size() > 0) {
                map.put("chipIdList", chipIdList);
            }
        }
        String nowDay = LocalDate.now().toString();
        // 只查询今天的告警 历史告警不处理
        if(date != null && date.contains(":")) {
            if (date.length() > 10 && date.substring(0, 10).equals(nowDay)) {
                map.put("createTime", nowDay);
                // 查询告警的chipIdList
                List<String> chipIdList = warningService.queryTodayWarningList(map);
                // 如果chipId存在告警 就把状态置为3 1 打开 2 关闭 3 有告警
                componentControlList.forEach(comControlModel ->
                {
                    if (chipIdList.contains(comControlModel.getChipId()) && comControlModel.getStatus() == 1) {
                        comControlModel.setStatus(3);
                    }
                });
            }
        }


        return componentControlList;
    }

    // 滑动时间轴 点击向左箭头或向右箭头  调用 queryComponentListByMinute.api 只刷新电气信息
    @RequestMapping(value = "/queryComponentListByMinute.api")
    public CallResult queryComponentListByMinute(@RequestParam(value = "powerStationId", required = true) String powerStationId,
                                                 @RequestParam(value = "dateTime", required = false) String dateTime,
                                                 @RequestParam(value = "language", required = false) String language,
                                                 @RequestParam(value = "groupId", required = false) String groupId) {

        CallResult callResult = CallResult.newInstance();
        int timeStep = ConfigUtil.getTimeStep();
        Map<String, Object> map = new HashMap<>();
        map.put("tableName", "_" + powerStationId);
        map.put("collectTimeStart", dateTime);
        map.put("collectTimeEnd", DateUtils.getTimeStepDate(dateTime, timeStep, true));
        List<ComponentElectricModel> comElectricModelList = new ArrayList<>();

        // 如果组串id为空 查询全站
        if (groupId == null) {
            List<ComponentViewModel> comViewModelList = componentCollectService.queryPastCollectInfo(map);
            map.clear();
            if (CollectionUtils.isNotEmpty(comViewModelList)) {
                comElectricModelList = comViewModelList.stream().map(comViewModel -> {
                    ComponentElectricModel comElectricModel = new ComponentElectricModel();
                    comElectricModel.setComponentTemperature(comViewModel.getComponentTemperature());
                    comElectricModel.setChipId(comViewModel.getChipId());
                    comElectricModel.setOutputCurrent(comViewModel.getOutputCurrent());
                    comElectricModel.setOutputVoltage(comViewModel.getOutputVoltage());
                    return comElectricModel;
                }).collect(Collectors.toList());
            }
        } else {
            map.put("groupId", groupId);
            comElectricModelList = componentCollectService.queryGroupPastCollectInfo(map);
        }
        map.put("data", comElectricModelList);

        callResult.setReModel(map);
        return callResult;
    }

    // 手机app获取系统视图的接口  返回的信息包括组件的电气信息 组串的电气信息 总发电量  时间轴四个数据
    @RequestMapping(value = "/queryComponentListTo.api")
    public CallResult queryComponentListTo(@RequestParam(value = "powerStationId", required = true) String powerStationId,
                                         @RequestParam(value = "date", required = false) String date,
                                         @RequestParam(value = "groupId", required = false) String groupId,
                                         @RequestParam(value = "sunUpTime", required = false) String sunUpTime,
                                         @RequestParam(value = "sunDownTime", required = false) String sunDownTime,
                                         @RequestParam(value = "imei", required = false) String imei) {
        String type = "2";
        return querySystemViewDataTo(groupId, powerStationId, date, sunUpTime, sunDownTime, type,imei);
    }

    public CallResult querySystemViewDataTo(String groupId, String powerStationId,
                                          String date, String sunUpTime,
                                            String sunDownTime, String type,String imei) {
        CallResult callResult = CallResult.newInstance();
        // 返回系统视图需要的数据
        //
        Map<String, Object> map = getElectricData(groupId, powerStationId, date, sunUpTime, sunDownTime, type);
        List<ComponentViewModel> list = (List)map.get("data");
        /*List<ComponentViewModelToVo> componentViewModelToVoList = new ArrayList<>();
        BeanUtils.copyProperties(list, componentViewModelToVoList);
        map.remove("data");
        map.put("data", componentViewModelToVoList);*/
        /*List<ComponentViewModelToVo> componentViewModelToVoList = new ArrayList<>();
        for (ComponentViewModel componentViewModel : list) {
            for (ComponentViewModelToVo componentViewModelToVo : componentViewModelToVoList) {
                componentViewModelToVo.setComponentTemperature(componentViewModel.getComponentTemperature());
                componentViewModelToVo.setChipId(componentViewModel.getChipId());
                componentViewModelToVo.setGroupId(componentViewModel.getGroupId());
                componentViewModelToVo.setOutputCurrent(componentViewModel.getOutputCurrent());
                componentViewModelToVo.setOutputVoltage(componentViewModel.getOutputVoltage());
                componentViewModelToVo.setHv(componentViewModel.getHv());
                componentViewModelToVo.setGapTop(componentViewModel.getGapTop());
                componentViewModelToVo.setGapLeft(componentViewModel.getGapLeft());
                componentViewModelToVo.setStatus(componentViewModel.getStatus());
                componentViewModelToVo.setCollectTime(componentViewModel.getCollectTime());
                componentViewModelToVo.setEquipmentId(componentViewModel.getEquipmentId());
                componentViewModelToVo.setPower(componentViewModel.getOutputCurrent()*componentViewModel.getOutputVoltage());
                componentViewModelToVoList.add(componentViewModelToVo);
            }
        }*/
        for (ComponentViewModel componentViewModel : list) {
            if(componentViewModel.getOutputVoltage() != null && componentViewModel.getOutputCurrent() != null) {
                if (componentViewModel.getOutputVoltage() != 0 && componentViewModel.getOutputCurrent() != 0){
                    componentViewModel.setPower(componentViewModel.getOutputCurrent()*componentViewModel.getOutputVoltage());
                }
            }
        }
        map.put("data", list);
        /*map.remove("data");
        map.put("data", componentViewModelToVoList);*/

        // 查询分组信息
        if (groupId == null) {
            List<GroupModel> groupModelList = componentGroupService.queryGroupInfoForApp(powerStationId);
            map.put("groupList", groupModelList);
        }
        List<CloudTerminalModelVo> collectorList=cloudTerminalService.queryCollectorListByPowerIdAndImei(powerStationId,imei);
        map.put("collectorList",collectorList);
        callResult.setReModel(map);
        return callResult;
    }

    // 返回系统视图需要的数据,加一个power,给三晶使用
    public Map<String, Object> getElectricDataTo(String groupId, String powerStationId,
                                               String date, String sunUpTime, String sunDownTime, String type) {

        // 先获取位置和告警信息对象 再获取电气信息对象 然后合并两个对象
        List<ComponentControlModel> componentControlList = getComPositionAndWarnStatus(groupId, powerStationId, date);

        Map<String, Object> queryMap = getQueryMap(powerStationId,date);
        List<ComponentViewModel> componentViewModelList = getComponentViewList(groupId,date,componentControlList,queryMap);

        //复制
        List<ComponentViewModelToVo> list = new ArrayList<>();
        BeanUtils.copyProperties(componentControlList, list);

        Map<String, Object> returnMap =new HashMap<>();
        returnMap.put("data", list);

        // 设置时间轴信息
        if (type.equals("1")) {
            String collectTime = null;
            if (componentViewModelList.size() > 0) {
                collectTime = componentViewModelList.get(0).getCollectTime();
                logger.info("first collect time:" + collectTime);
            }
            String timeAxisCreateTime = (String) queryMap.get("collectTimeStart");
            timeAxisCreateTime = collectTime != null ? collectTime : timeAxisCreateTime + ":00";

            returnMap.put("timeAxis", Collections.singletonList(timeAxisCreateTime));
            returnMap.put("timeAxisStep", ConfigUtil.getTimeStep());
            String day = timeAxisCreateTime.substring(0, 10);
            returnMap.put("timeAxisBeginTime", day + " " + sunUpTime + ":00");
            returnMap.put("timeAxisEndTime", day + " " + sunDownTime + ":00");
        }
        return returnMap;
    }

    private List<ComponentViewModel> getComponentViewListTo(String groupId,String date,
                                                          List<ComponentControlModel> componentControlList,Map<String, Object> map) {
        if (groupId != null) {
            List<String> chipIdList = componentControlList.stream().map(ComponentControlModel::getChipId).collect(Collectors.toList());
            map.put("chipIdList", chipIdList);
        }

        List<ComponentViewModel> componentViewModelList;
        if (date != null && date.contains(":")) {
            map.put("limit", componentControlList.size() * 2);
            componentViewModelList = componentCollectService.queryLastCollectInfo(map);
            if (componentViewModelList.size() != 0) {
                // 获取每个批次的组件数量  最后是否需要batchNo 还是只需要采集时间
                Map<String, Long> collectTimeMap = Optional.of(componentViewModelList).
                        orElse(new ArrayList<>()).stream().
                        collect(Collectors.groupingBy(ComponentViewModel::getCollectTime, Collectors.counting()));

                collectTimeMap.forEach((k, v) -> {
                    logger.info("key:value = " + k + ":" + v);
                });
                String maxCollectTime = getMaxCollectTime(collectTimeMap);

                logger.info("maxCollectTime: " + maxCollectTime);
                // 用这个批次号去过滤组件
                componentViewModelList = Optional.of(componentViewModelList).
                        orElse(new ArrayList<>()).stream().
                        filter(componentViewModelToVo -> componentViewModelToVo.getCollectTime().equals(maxCollectTime))
                        .collect(Collectors.toList());
            }
        } else {
            componentViewModelList = componentCollectService.queryPastCollectInfo(map);
        }



        // key chipId value 为ComponentViewModel
        Map<String, ComponentViewModel> comViewModelMap = Optional.of(componentViewModelList).
                orElse(new ArrayList<>()).stream().
                collect(Collectors.toMap(ComponentViewModel::getChipId, v -> v, (a, b) -> a));

        List<ComponentViewModel> returnComViewModelList = new ArrayList<>();
        ComponentViewModel componentViewModel;

        // 循环componentControlList  合并电气信息对象
        for (ComponentControlModel componentControlModel : componentControlList) {
            componentViewModel = new ComponentViewModel(componentControlModel,
                    comViewModelMap.get(componentControlModel.getChipId()));
            returnComViewModelList.add(componentViewModel);
        }
        return returnComViewModelList;
    }

}
